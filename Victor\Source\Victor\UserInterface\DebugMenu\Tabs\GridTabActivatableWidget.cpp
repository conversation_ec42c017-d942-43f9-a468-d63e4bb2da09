// Fill out your copyright notice in the Description page of Project Settings.


#include "GridTabActivatableWidget.h"
#include "Kismet/GameplayStatics.h"
#include "Victor/Core/Grid/Grid.h"
#include "Components/ComboBoxString.h"

void UGridTabActivatableWidget::NativeOnInitialized()
{
	Super::NativeOnInitialized();

	CurrentGrid = Cast<AGrid>( UGameplayStatics::GetActorOfClass( GetWorld(), AGrid::StaticClass() ) );

	if( ShapeComboBox )
	{
		ShapeComboBox->OnSelectionChanged.AddDynamic( this, &UGridTabActivatableWidget::OnShapeComboBoxChange );
	}
}

void UGridTabActivatableWidget::OnShapeComboBoxChange( FString NewValue )
{

}

void UGridTabActivatableWidget::OnLocationSpinBoxChange( FVector NewValue )
{

}

void UGridTabActivatableWidget::OnTileCountSpinBoxChange( FVector2D NewValue )
{

}

void UGridTabActivatableWidget::OnTileSizeSpinBoxChange()
{

}
// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "GridTabActivatableWidget.generated.h"

class UComboBoxString;
class UDebugSliderVector3Widget;
class UDebugSliderVector2Widget;

/**
 *
 */
UCLASS()
class VICTOR_API UGridTabActivatableWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

	//Fields
private:
	AGrid* CurrentGrid;

protected:
	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UComboBoxString* ShapeComboBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderVector3Widget* LocationSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderVector2Widget* TileCountSpinBox;

	UPROPERTY( meta = ( BindWidget ), BlueprintReadOnly )
	UDebugSliderVector3Widget* TileSizeSpinBox;


	//Functions
private:
	void OnShapeComboBoxChange( FString NewValue, ESelectInfo::Type SelectionType );

	void OnLocationSpinBoxChange( FVector NewValue );

	void OnTileCountSpinBoxChange( FVector2D NewValue );

	void OnTileSizeSpinBoxChange();

public:
	void NativeOnInitialized() override;


	//TODO Set functions to set values in CurrentGrid
};
